<?php

// Iniciar sesión si es necesario
use App\classes\Activo;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lactivos.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$activos = []; // Initialize as an empty array
#endregion init variables

#region region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region region Handle POST Actions (Deactivation)
// --- Handle Non-AJAX POST Action (Deactivate Activo) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$activoIdToDeactivate = filter_input(INPUT_POST, 'activoId', FILTER_VALIDATE_INT);
	
	if ($activoIdToDeactivate) {
		try {
			$success = Activo::desactivar($activoIdToDeactivate, $conexion);
			
			if ($success) {
				$_SESSION['flash_message_success'] = "Activo desactivado correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar el activo.";
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al desactivar activo: " . $e->getMessage();
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de activo inválido para desactivar.";
	}
	
	// Redirect back to the activo list page after processing
	header('Location: lactivos');
	exit;
}
#endregion Handle POST Actions

#region region Handle POST Actions (Activation)
// --- Handle Non-AJAX POST Action (Activate Activo) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'activar') {
	$activoIdToActivate = filter_input(INPUT_POST, 'activoId', FILTER_VALIDATE_INT);
	
	if ($activoIdToActivate) {
		try {
			$success = Activo::activar($activoIdToActivate, $conexion);
			
			if ($success) {
				$_SESSION['flash_message_success'] = "Activo activado correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o activar el activo.";
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al activar activo: " . $e->getMessage();
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de activo inválido para activar.";
	}
	
	// Redirect back to the activo list page after processing
	header('Location: lactivos');
	exit;
}
#endregion Handle POST Actions

#region try
try {
	$activos = Activo::get_list($conexion);
	
} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de activos.";
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de activos: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lactivos.view.php';

?>
