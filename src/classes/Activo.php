<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Activo
{
	// --- Atributos ---
	private ?int    $id                = null;
	private ?string $descripcion       = null;
	private ?int    $estado            = null;
	private ?string $marca             = null;
	private ?string $modelo            = null;
	private ?string $numero_serie      = null;
	private ?string $fecha_adquisicion = null;
	private ?string $caracteristicas   = null;
	private ?float  $valor_activo      = null;
	private ?string $comentarios       = null;
	
	/**
	 * Constructor: Inicializa las propiedades del objeto Activo.
	 */
	public function __construct()
	{
		$this->id                = 0; // O null si prefieres no usar 0 por defecto
		$this->descripcion       = null;
		$this->estado            = 1; // Estado activo por defecto
		$this->marca             = null;
		$this->modelo            = null;
		$this->numero_serie      = null;
		$this->fecha_adquisicion = null;
		$this->caracteristicas   = null;
		$this->valor_activo      = 0;
		$this->comentarios       = null;
	}
	
	/**
	 * Método estático para construir un objeto Activo desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del activo.
	 *
	 * @return self Instancia de Activo.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                    = new self();
			$objeto->id                = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->descripcion       = $resultado['descripcion'] ?? null;
			$objeto->estado            = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			$objeto->marca             = $resultado['marca'] ?? null;
			$objeto->modelo            = $resultado['modelo'] ?? null;
			$objeto->numero_serie      = $resultado['numero_serie'] ?? null;
			$objeto->fecha_adquisicion = $resultado['fecha_adquisicion'] ?? null;
			$objeto->caracteristicas   = $resultado['caracteristicas'] ?? null;
			$objeto->valor_activo      = isset($resultado['valor_activo']) ? (float)$resultado['valor_activo'] : null;
			$objeto->comentarios       = $resultado['comentarios'] ?? null;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Activo: " . $e->getMessage());
		}
	}
	
	// --- Métodos de Acceso a Datos (Estáticos) ---
	
	/**
	 * Obtiene un activo por su ID.
	 *
	 * @param int $id       ID del activo.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Activo o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener activo por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM activos
            WHERE
            	id = :id
            LIMIT 1
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			return $resultado ? self::construct($resultado) : null;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener Activo (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de activos activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Activo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de activos activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM activos
            WHERE
            	estado = 1
            ORDER BY
            	descripcion
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Activos: " . $e->getMessage());
		}
	}
	
	/**
	 * Crea un nuevo activo en la base de datos a partir de un objeto Activo.
	 * El objeto Activo debe tener la descripción establecida.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo activo creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getDescripcion())) {
			throw new Exception("La descripción es requerida para crear un activo.");
		}
		
		try {
			$descripcion = $this->getDescripcion(); // Para usar en mensaje de error
			
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO activos (
            	 descripcion
            	,estado
            	,marca
            	,modelo
            	,numero_serie
            	,fecha_adquisicion
            	,caracteristicas
            	,valor_activo
            	,comentarios
            ) VALUES (
            	 :descripcion
            	,:estado
            	,:marca
            	,:modelo
            	,:numero_serie
            	,:fecha_adquisicion
            	,:caracteristicas
            	,:valor_activo
            	,:comentarios
            )
            SQL;
			
			$statement = $conexion->prepare($query);
			
			// Bind de parámetros desde el objeto
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->getEstado() ?? 1, PDO::PARAM_INT);
			$statement->bindValue(':marca', $this->getMarca(), PDO::PARAM_STR);
			$statement->bindValue(':modelo', $this->getModelo(), PDO::PARAM_STR);
			$statement->bindValue(':numero_serie', $this->getNumeroSerie(), PDO::PARAM_STR);
			$statement->bindValue(':fecha_adquisicion', $this->getFechaAdquisicion(), PDO::PARAM_STR);
			$statement->bindValue(':caracteristicas', $this->getCaracteristicas(), PDO::PARAM_STR);
			// Para valor_activo usamos PDO::PARAM_STR ya que PDO no tiene un tipo específico para float,
			// pero la validación en setValorActivo asegura que siempre sea un número
			$statement->bindValue(':valor_activo', $this->getValorActivo(), PDO::PARAM_STR);
			$statement->bindValue(':comentarios', $this->getComentarios(), PDO::PARAM_STR);
			
			// Ejecutar la consulta
			$success = $statement->execute();
			
			if ($success) {
				// Devolver el ID del activo recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}
			
		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear activo: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear activo: " . $e->getMessage());
		}
	}
	
	/**
	 * Modifica un activo existente con todos sus campos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si la descripción está vacía o si ocurre un error de base de datos.
	 */
	public function modificarCompleto(PDO $conexion): bool
	{
		// Validaciones básicas
		if (empty($this->getDescripcion())) {
			throw new Exception("La descripción no puede estar vacía.");
		}
		
		if ($this->getId() <= 0) {
			throw new Exception("ID de activo inválido para modificación.");
		}
		
		try {
			// Consulta para actualizar todos los campos
			$query = <<<SQL
            UPDATE activos SET
                 descripcion = :descripcion
                ,estado = :estado
                ,marca = :marca
                ,modelo = :modelo
                ,numero_serie = :numero_serie
                ,fecha_adquisicion = :fecha_adquisicion
                ,caracteristicas = :caracteristicas
                ,valor_activo = :valor_activo
                ,comentarios = :comentarios
            WHERE
                id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			
			// Bind de todos los parámetros
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':marca', $this->getMarca(), PDO::PARAM_STR);
			$statement->bindValue(':modelo', $this->getModelo(), PDO::PARAM_STR);
			$statement->bindValue(':numero_serie', $this->getNumeroSerie(), PDO::PARAM_STR);
			$statement->bindValue(':fecha_adquisicion', $this->getFechaAdquisicion(), PDO::PARAM_STR);
			$statement->bindValue(':caracteristicas', $this->getCaracteristicas(), PDO::PARAM_STR);
			// Para valor_activo usamos PDO::PARAM_STR ya que PDO no tiene un tipo específico para float,
			// pero la validación en setValorActivo asegura que siempre sea un número
			$statement->bindValue(':valor_activo', $this->getValorActivo(), PDO::PARAM_STR);
			$statement->bindValue(':comentarios', $this->getComentarios(), PDO::PARAM_STR);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);
			
			// execute() devuelve true en éxito, false en error.
			return $statement->execute();
			
		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar activo (ID: {$this->getId()}): " . $e->getMessage());
		}
	}
	
	/**
	 * Modifica la descripción de un activo existente.
	 * Método mantenido por compatibilidad con código existente.
	 *
	 * @param int    $id              ID del activo a modificar.
	 * @param string $nuevaDescripcion La nueva descripción para el activo.
	 * @param PDO    $conexion        Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si la nueva descripción está vacía o si ocurre un error de base de datos.
	 */
	public static function modificar(int $id, string $nuevaDescripcion, PDO $conexion): bool
	{
		if (empty(trim($nuevaDescripcion))) {
			throw new Exception("La descripción no puede estar vacía.");
		}
		
		try {
			// Consulta para actualizar la descripción
			$query = <<<SQL
            UPDATE activos SET
                descripcion = :descripcion
            WHERE
                id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':descripcion', trim($nuevaDescripcion), PDO::PARAM_STR);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			
			// execute() devuelve true en éxito, false en error.
			return $statement->execute();
			
		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar activo (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Desactiva un activo estableciendo su estado a 0.
	 *
	 * @param int $id       ID del activo a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE activos SET
            	estado = 0
            WHERE
            	id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			
			// execute() devuelve true en éxito, false en error.
			return $statement->execute();
			
		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al desactivar activo (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Activa un activo estableciendo su estado a 1.
	 *
	 * @param int $id       ID del activo a activar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la activación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function activar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 1 (activo)
			$query = <<<SQL
            UPDATE activos SET
            	estado = 1
            WHERE
            	id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			
			// execute() devuelve true en éxito, false en error.
			return $statement->execute();
			
		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al activar activo (ID: $id): " . $e->getMessage());
		}
	}
	
	// --- Getters y Setters ---
	
	public function getId(): ?int
	{
		return $this->id;
	}
	
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}
	
	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}
	
	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}
	
	public function getEstado(): ?int
	{
		return $this->estado;
	}
	
	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}
	
	public function getMarca(): ?string
	{
		return $this->marca;
	}
	
	public function setMarca(?string $marca): self
	{
		$this->marca = $marca;
		return $this;
	}
	
	public function getModelo(): ?string
	{
		return $this->modelo;
	}
	
	public function setModelo(?string $modelo): self
	{
		$this->modelo = $modelo;
		return $this;
	}

	public function getNumeroSerie(): ?string
	{
		return $this->numero_serie;
	}

	public function setNumeroSerie(?string $numero_serie): self
	{
		$this->numero_serie = $numero_serie;
		return $this;
	}
	
	public function getFechaAdquisicion(): ?string
	{
		return $this->fecha_adquisicion;
	}
	
	public function setFechaAdquisicion(?string $fecha_adquisicion): self
	{
		$this->fecha_adquisicion = $fecha_adquisicion;
		return $this;
	}
	
	public function getCaracteristicas(): ?string
	{
		return $this->caracteristicas;
	}
	
	public function setCaracteristicas(?string $caracteristicas): self
	{
		$this->caracteristicas = $caracteristicas;
		return $this;
	}
	
	public function getValorActivo(): ?float
	{
		return $this->valor_activo;
	}
	
	/**
	 * Establece el valor del activo, asegurando que sea un número o null.
	 * 
	 * @param mixed $valor_activo El valor a establecer
	 * @return self
	 * @throws Exception Si el valor no es numérico ni null
	 */
	public function setValorActivo($valor_activo): self
	{
		// Si es null, aceptamos el valor
		if ($valor_activo === null) {
			$this->valor_activo = 0;
			return $this;
		}
		
		// Convertir el valor a string para poder usar str_replace de forma segura
        // Esto maneja tanto strings como números (int/float)
        $valor_string = (string)$valor_activo;

        // Si la cadena está vacía después de quitar espacios, asignamos 0.0
        if (trim($valor_string) === '') {
             $this->valor_activo = 0.0;
             return $this;
        }

        // 1. Definir los caracteres a eliminar (solo relevantes si era string originalmente)
        $caracteres_a_eliminar = ['$', ' ']; // Quitamos el punto de aquí, ya que podría ser un decimal válido

        // 2. Reemplazar la coma decimal por un punto
        $valor_limpio = str_replace(',', '.', $valor_string);

        // 3. Eliminar los caracteres no deseados ($ y espacio)
        $valor_limpio = str_replace($caracteres_a_eliminar, '', $valor_limpio);

        // 4. Eliminar puntos que actúan como separadores de miles
        // Esto se hace después para no eliminar el punto decimal si ya existía o se convirtió desde coma
        // Se elimina solo si hay más de un punto o si el punto no está seguido de dígitos al final (heurística)
        if (substr_count($valor_limpio, '.') > 1) {
            // Si hay más de un punto, asumimos que son separadores de miles y los quitamos todos menos el último
            $partes = explode('.', $valor_limpio);
            $parte_entera = implode('', array_slice($partes, 0, -1)); // Une todo menos el último elemento
            $parte_decimal = end($partes); // Toma el último elemento (decimales)
            $valor_limpio = $parte_entera . '.' . $parte_decimal;
        }


        // 5. Convertir a float
        $valor_float = filter_var($valor_limpio, FILTER_VALIDATE_FLOAT);

        // Si la conversión falla, asignar 0.0
        $this->valor_activo = ($valor_float !== false) ? $valor_float : 0.0;
		
		return $this;
	}
	
	public function getComentarios(): ?string
	{
		return $this->comentarios;
	}
	
	public function setComentarios(?string $comentarios): self
	{
		$this->comentarios = $comentarios;
		return $this;
	}
	
	// --- Métodos adicionales ---
	
	/**
	 * Verifica si el activo está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}
}
