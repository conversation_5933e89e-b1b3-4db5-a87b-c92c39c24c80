<?php
#region region DOCS

/** @var Activo[] $activos */

use App\classes\Activo;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Activos</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Activos</h4>
				<p class="mb-0 text-muted">Administra los activos del sistema</p>
			</div>
			<div class="ms-auto">
				<a href="iactivo" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo</a>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region PANEL ACTIVOS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Activos
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE ACTIVOS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th>Acciones</th>
						<th>Descripción</th>
						<th>Marca</th>
						<th>Modelo</th>
						<th>Número Serie</th>
						<th class="text-center">Fecha Adquisición</th>
						<th class="text-center">Valor Activo</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="activo-table-body">
					<?php foreach ($activos as $activo): ?>
						<tr data-activo-id="<?php echo $activo->getId(); ?>">
							<td>
								<?php // Edit Button - Links to eactivo page ?>
								<a href="eactivo?id=<?php echo $activo->getId(); ?>" class="btn btn-xs btn-primary me-1"
								   title="Editar Activo">
									<i class="fa fa-edit"></i>
								</a>
								<?php // View Images Button ?>
								<button type="button" class="btn btn-xs btn-info me-1 btn-ver-imagenes"
								        title="Ver Imágenes"
								        data-activoid="<?php echo $activo->getId(); ?>"
								        data-descripcion="<?php echo htmlspecialchars($activo->getDescripcion() ?? ''); ?>">
									<i class="fa fa-images"></i>
								</button>
								<?php // Deactivate/Activate Button ?>
								<?php if ($activo->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-activo"
									        title="Desactivar"
									        data-activoid="<?php echo $activo->getId(); ?>"
									        data-descripcion="<?php echo htmlspecialchars($activo->getDescripcion() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php else: ?>
									<button type="button" class="btn btn-xs btn-success btn-activar-activo"
									        title="Activar"
									        data-activoid="<?php echo $activo->getId(); ?>"
									        data-descripcion="<?php echo htmlspecialchars($activo->getDescripcion() ?? ''); ?>">
										<i class="fa fa-check"></i>
									</button>
								<?php endif; ?>
							</td>
							<td><?php echo htmlspecialchars($activo->getDescripcion()); ?></td>
							<td><?php echo htmlspecialchars($activo->getMarca() ?? ''); ?></td>
							<td><?php echo htmlspecialchars($activo->getModelo() ?? ''); ?></td>
							<td><?php echo htmlspecialchars($activo->getNumeroSerie() ?? ''); ?></td>
							<td class="text-center"><?php echo $activo->getFechaAdquisicion() ? $activo->getFechaAdquisicion() : ''; ?></td>
							<td class="text-end"><?php echo $activo->getValorActivo() ? '$' . number_format($activo->getValorActivo(), 0, '', '.') : ''; ?></td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($activos)): ?>
						<tr>
							<td colspan="8" class="text-center">No hay activos para mostrar.</td>
						</tr>
					<?php endif; ?>
					
					</tbody>
				</table>
				<?php #endregion TABLE ACTIVOS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL ACTIVOS ?>
	
	</div>
	<!-- END #content -->
	
	<!-- Modal for displaying images -->
	<div class="modal fade" id="imagenes-modal" tabindex="-1" role="dialog" aria-labelledby="imagenes-modal-label" aria-hidden="true">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="imagenes-modal-label">Imágenes del Activo</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div id="imagenes-container" class="d-flex flex-wrap gap-2">
						<!-- Images will be loaded here -->
					</div>
					<div id="no-imagenes-message" class="text-center p-3 d-none">
						<p>Este activo no tiene imágenes asociadas.</p>
					</div>
					<div id="loading-imagenes" class="text-center p-3">
						<span class="spinner-border spinner-border-sm"></span> Cargando imágenes...
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
				</div>
			</div>
		</div>
	</div>
	
	<?php #region region Hidden Forms for Activation/Deactivation ?>
	<form id="deactivate-activo-form" method="POST" action="lactivos" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="activoId" id="deactivate-activo-id">
	</form>
	
	<form id="activate-activo-form" method="POST" action="lactivos" style="display: none;">
		<input type="hidden" name="action" value="activar">
		<input type="hidden" name="activoId" id="activate-activo-id">
	</form>
	<?php #endregion Hidden Forms ?>
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody = document.getElementById('activo-table-body');
        const imagenesModal = document.getElementById('imagenes-modal');
        const imagenesModalLabel = document.getElementById('imagenes-modal-label');
        const imagenesContainer = document.getElementById('imagenes-container');
        const noImagenesMessage = document.getElementById('no-imagenes-message');
        const loadingImagenes = document.getElementById('loading-imagenes');
        
        // Initialize the modal
        const modal = new bootstrap.Modal(imagenesModal);
        
        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton  = event.target.closest('.btn-desactivar-activo');
                const activateButton    = event.target.closest('.btn-activar-activo');
                const verImagenesButton = event.target.closest('.btn-ver-imagenes');
                
                // --- Handle Ver Imagenes Click ---
                if (verImagenesButton) {
                    event.preventDefault();
                    const activoId = verImagenesButton.dataset.activoid;
                    const descripcion = verImagenesButton.dataset.descripcion || 'Activo';
                    
                    // Update modal title
                    imagenesModalLabel.textContent = `Imágenes del Activo: ${descripcion}`;
                    
                    // Clear previous images
                    imagenesContainer.innerHTML = '';
                    
                    // Show loading indicator
                    loadingImagenes.classList.remove('d-none');
                    noImagenesMessage.classList.add('d-none');
                    
                    // Show the modal
                    modal.show();
                    
                    // Fetch images for this activo
                    fetch(`get_activo_imagenes?id_activo=${activoId}`)
                        .then(response => response.json())
                        .then(data => {
                            // Hide loading indicator
                            loadingImagenes.classList.add('d-none');
                            
                            if (data.length === 0) {
                                // Show no images message
                                noImagenesMessage.classList.remove('d-none');
                            } else {
                                // Display the images
                                data.forEach(imagen => {
                                    const imgContainer = document.createElement('div');
                                    imgContainer.className = 'image-item';
                                    
                                    const img = document.createElement('img');
                                    img.src = `resources/uploads/activos/${imagen.nombre_archivo}`;
                                    img.alt = `Imagen ${imagen.id}`;
                                    img.className = 'img-thumbnail';
                                    img.style.maxWidth = '200px';
                                    img.style.height = 'auto';
                                    
                                    imgContainer.appendChild(img);
                                    imagenesContainer.appendChild(imgContainer);
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching images:', error);
                            loadingImagenes.classList.add('d-none');
                            imagenesContainer.innerHTML = '<div class="alert alert-danger">Error al cargar las imágenes</div>';
                        });
                }
                
                // --- Handle Deactivate Click ---
                if (deactivateButton) {
                    event.preventDefault();
                    const activoId = deactivateButton.dataset.activoid;
                    const descripcion = deactivateButton.dataset.descripcion || 'este activo';
                    
                    swal({
                        title     : "Confirmar Desactivación",
                        text      : `¿Seguro que quieres desactivar el activo '${descripcion}'?`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                        dangerMode: true,
                    })
                        .then((willDelete) => {
                            if (willDelete) {
                                document.getElementById('deactivate-activo-id').value = activoId;
                                document.getElementById('deactivate-activo-form').submit();
                            }
                        });
                }
                
                // --- Handle Activate Click ---
                if (activateButton) {
                    event.preventDefault();
                    const activoId = activateButton.dataset.activoid;
                    const descripcion = activateButton.dataset.descripcion || 'este activo';
                    
                    swal({
                        title     : "Confirmar Activación",
                        text      : `¿Seguro que quieres activar el activo '${descripcion}'?`,
                        icon      : "question",
                        buttons   : {
                            cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-success", closeModal: true}
                        }
                    })
                        .then((willActivate) => {
                            if (willActivate) {
                                document.getElementById('activate-activo-id').value = activoId;
                                document.getElementById('activate-activo-form').submit();
                            }
                        });
                }
            });
        }
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
